import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/presentation/view_models/artisan/artisan_portfolio_view_model.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  late TextEditingController _nameController;
  late TextEditingController _locationController;
  late TextEditingController _aboutController;
  final bool _isUploading = false;
  final bool _isSaving = false; // Track saving state for the button

  @override
  void initState() {
    super.initState();

    // Initialize controllers with empty strings first
    _nameController = TextEditingController();
    _locationController = TextEditingController();
    _aboutController = TextEditingController();

    // Load data after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = ref.read(artisanPortfolioViewModelProvider);
      final artisanData = state.artisanProfileData;

      if (artisanData != null) {
        _nameController.text = artisanData.name ?? '';
        _locationController.text = artisanData.address ?? '';
        _aboutController.text = artisanData.about ?? '';
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _locationController.dispose();
    _aboutController.dispose();
    super.dispose();
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    int maxLines = 1,
    bool useSemiBold = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: MyTypography.SemiBold.copyWith(
            color: Colors.grey[800],
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          style:
              useSemiBold
                  ? MyTypography.SemiBold.copyWith(
                    color: Colors.grey[800],
                    fontSize: 15,
                  )
                  : MyTypography.Regular.copyWith(
                    color: Colors.grey[800],
                    fontSize: 15,
                  ),
          decoration: InputDecoration(
            hintText: 'Enter $label',
            hintStyle: MyTypography.Regular.copyWith(
              color: Colors.grey[500],
              fontSize: 15,
            ),
            filled: true,
            fillColor: Colors.grey[100],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildCategoryDropdown() {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    final artisanData = state.artisanProfileData;

    if (artisanData == null || artisanData.specializations == null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profession',
            style: MyTypography.SemiBold.copyWith(
              color: Colors.grey[800],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Loading profession data...',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          const SizedBox(height: 24),
        ],
      );
    }

    // Get profession from specializations
    final profession =
        artisanData.specializations?.isNotEmpty == true
            ? artisanData.specializations![0].services?.name ?? 'Other'
            : 'Other';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profession',
          style: MyTypography.SemiBold.copyWith(
            color: Colors.grey[800],
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.teal[700],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            profession,
            style: MyTypography.Bold.copyWith(
              color: Colors.white,
              fontSize: 15,
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSpecializationsSelector() {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    final artisanData = state.artisanProfileData;

    if (artisanData == null || artisanData.specializations == null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Specializations',
            style: MyTypography.SemiBold.copyWith(
              color: Colors.grey[800],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Loading specializations...',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          const SizedBox(height: 24),
        ],
      );
    }

    // Get specializations from artisan data
    final specializations = <String>[];

    for (final spec in artisanData.specializations ?? []) {
      for (final tag in spec.specializationTags ?? []) {
        if (tag.subCategories?.name != null) {
          specializations.add(tag.subCategories!.name!);
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Specializations',
          style: MyTypography.SemiBold.copyWith(
            color: Colors.grey[800],
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 12,
          children:
              specializations.map((spec) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.teal[700],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    spec,
                    style: MyTypography.Medium.copyWith(color: Colors.white),
                  ),
                );
              }).toList(),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildCoverImageSelector() {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    final viewModel = ref.read(artisanPortfolioViewModelProvider.notifier);
    final artisanData = state.artisanProfileData;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cover Image',
          style: MyTypography.SemiBold.copyWith(
            color: Colors.grey[800],
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap:
              viewModel.isUploadingCoverImage
                  ? null
                  : () => viewModel.selectAndUploadCoverImage(context),
          child: Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
              image:
                  viewModel.coverImageFile != null
                      ? DecorationImage(
                        image: FileImage(viewModel.coverImageFile!),
                        fit: BoxFit.cover,
                        opacity: viewModel.isUploadingCoverImage ? 0.5 : 1.0,
                      )
                      : (artisanData?.coverPhoto != null &&
                              artisanData!.coverPhoto!.isNotEmpty
                          ? DecorationImage(
                            image: NetworkImage(artisanData.coverPhoto!),
                            fit: BoxFit.cover,
                            opacity:
                                viewModel.isUploadingCoverImage ? 0.5 : 1.0,
                          )
                          : null),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (viewModel.coverImageFile == null &&
                    (artisanData?.coverPhoto == null ||
                        artisanData!.coverPhoto!.isEmpty))
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_a_photo,
                        color: Colors.grey[600],
                        size: 40,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Add Cover Photo',
                        style: MyTypography.Medium.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                if (viewModel.isUploadingCoverImage)
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        value: viewModel.coverImageUploadProgress,
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${(viewModel.coverImageUploadProgress * 100).toInt()}%',
                        style: MyTypography.Medium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                if (!viewModel.isUploadingCoverImage &&
                    (viewModel.coverImageFile != null ||
                        (artisanData?.coverPhoto != null &&
                            artisanData!.coverPhoto!.isNotEmpty)))
                  Positioned(
                    bottom: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha((0.8 * 255).round()),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.edit, color: Colors.teal),
                    ),
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildAvatarImageSelector() {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    final viewModel = ref.read(artisanPortfolioViewModelProvider.notifier);
    final artisanData = state.artisanProfileData;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Image',
          style: MyTypography.SemiBold.copyWith(
            color: Colors.grey[800],
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: GestureDetector(
            onTap:
                viewModel.isUploadingAvatarImage
                    ? null
                    : () => viewModel.selectAndUploadAvatarImage(context),
            child: Stack(
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[200],
                    image:
                        viewModel.avatarImageFile != null
                            ? DecorationImage(
                              image: FileImage(viewModel.avatarImageFile!),
                              fit: BoxFit.cover,
                              opacity:
                                  viewModel.isUploadingAvatarImage ? 0.5 : 1.0,
                            )
                            : (artisanData?.avatar != null &&
                                    artisanData!.avatar!.isNotEmpty
                                ? DecorationImage(
                                  image: NetworkImage(artisanData.avatar!),
                                  fit: BoxFit.cover,
                                  opacity:
                                      viewModel.isUploadingAvatarImage
                                          ? 0.5
                                          : 1.0,
                                )
                                : null),
                  ),
                  child:
                      viewModel.avatarImageFile == null &&
                              (artisanData?.avatar == null ||
                                  artisanData!.avatar!.isEmpty)
                          ? Icon(
                            Icons.person,
                            size: 60,
                            color: Colors.grey[400],
                          )
                          : null,
                ),
                if (viewModel.isUploadingAvatarImage)
                  Positioned.fill(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            value: viewModel.avatarImageUploadProgress,
                            color: Colors.white,
                            strokeWidth: 3,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${(viewModel.avatarImageUploadProgress * 100).toInt()}%',
                            style: MyTypography.Medium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                if (!viewModel.isUploadingAvatarImage)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.teal[700],
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  // Widget _buildImageSelector({
  //   required String label,
  //   required String currentImageUrl,
  //   required VoidCallback onTap,
  //   required double height,
  //   bool isProfile = false,
  // }) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Text(
  //         label,
  //         style: MyTypography.SemiBold.copyWith(
  //           color: Colors.grey[800],
  //           fontSize: 16,
  //         ),
  //       ),
  //       const SizedBox(height: 8),
  //       GestureDetector(
  //         onTap: onTap,
  //         child: Container(
  //           height: height,
  //           width: double.infinity,
  //           decoration: BoxDecoration(
  //             color: Colors.grey[200],
  //             borderRadius: BorderRadius.circular(12),
  //             image: DecorationImage(
  //               image:
  //                   currentImageUrl.startsWith('http')
  //                       ? NetworkImage(currentImageUrl) as ImageProvider
  //                       : AssetImage(currentImageUrl),
  //               fit: BoxFit.cover,
  //             ),
  //           ),
  //           child: Stack(
  //             alignment: isProfile ? Alignment.center : Alignment.bottomRight,
  //             children: [
  //               if (isProfile)
  //                 CircleAvatar(
  //                   radius: 60,
  //                   backgroundImage:
  //                       currentImageUrl.startsWith('http')
  //                           ? NetworkImage(currentImageUrl) as ImageProvider
  //                           : AssetImage(currentImageUrl),
  //                 ),
  //               Container(
  //                 margin: const EdgeInsets.all(16),
  //                 padding: const EdgeInsets.all(8),
  //                 decoration: BoxDecoration(
  //                   color: Colors.white,
  //                   shape: BoxShape.circle,
  //                   boxShadow: [
  //                     BoxShadow(
  //                       color: Colors.black.withAlpha((0.1 * 255).round()),
  //                       blurRadius: 8,
  //                       offset: const Offset(0, 2),
  //                     ),
  //                   ],
  //                 ),
  //                 child: const Icon(Icons.camera_alt, color: Colors.teal),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //       const SizedBox(height: 24),
  //     ],
  //   );
  // }

  // void _showImageSourceDialog({required bool isProfileImage}) {
  //   showModalBottomSheet(
  //     context: context,
  //     shape: const RoundedRectangleBorder(
  //       borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //     ),
  //     builder:
  //         (context) => Padding(
  //           padding: const EdgeInsets.all(20),
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               Text(
  //                 'Select Image Source',
  //                 style: MyTypography.SemiBold.copyWith(fontSize: 18),
  //               ),
  //               const SizedBox(height: 20),
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  //                 children: [
  //                   _buildImageSourceOption(
  //                     icon: Icons.camera_alt,
  //                     label: 'Camera',
  //                     onTap: () {
  //                       Navigator.pop(context);
  //                       _simulateImageUpload();
  //                     },
  //                   ),
  //                   _buildImageSourceOption(
  //                     icon: Icons.photo_library,
  //                     label: 'Gallery',
  //                     onTap: () {
  //                       Navigator.pop(context);
  //                       _simulateImageUpload();
  //                     },
  //                   ),
  //                 ],
  //               ),
  //             ],
  //           ),
  //         ),
  //   );
  // }

  // Widget _buildImageSourceOption({
  //   required IconData icon,
  //   required String label,
  //   required VoidCallback onTap,
  // }) {
  //   return GestureDetector(
  //     onTap: onTap,
  //     child: Column(
  //       children: [
  //         Container(
  //           padding: const EdgeInsets.all(16),
  //           decoration: BoxDecoration(
  //             color: Colors.teal[50],
  //             shape: BoxShape.circle,
  //           ),
  //           child: Icon(icon, color: Colors.teal[700], size: 32),
  //         ),
  //         const SizedBox(height: 8),
  //         Text(label, style: MyTypography.Medium),
  //       ],
  //     ),
  //   );
  // }

  // void _simulateImageUpload() {
  //   setState(() {
  //     _isUploading = true;
  //   });

  //   // Simulate network delay
  //   Future.delayed(const Duration(seconds: 2), () {
  //     setState(() {
  //       _isUploading = false;
  //     });

  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('Image uploaded successfully'),
  //         duration: Duration(seconds: 2),
  //       ),
  //     );
  //   });
  // }

  void _saveProfile() async {
    final viewModel = ref.read(artisanPortfolioViewModelProvider.notifier);

    try {
      // Save profile changes
      final success = await viewModel.saveProfile(
        name: _nameController.text,
        about: _aboutController.text,
        location: _locationController.text,
      );

      if (success) {
        // Return to previous screen
        if (mounted) {
          context.pop();
          _showSuccessMessage('Profile updated successfully');
        }
      } else {
        // Show error message
        if (context.mounted) {
          // ScaffoldMessenger.of(context).showSnackBar(
          //   const SnackBar(
          //     content: Text('Failed to update profile. Please try again.'),
          //     backgroundColor: Colors.red,
          //   ),
          // );
          _showErrorMessage('Failed to update profile. Please try again.');
        }
      }
    } catch (e) {
      // Show error message
      if (context.mounted) {
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text('Error: ${e.toString()}'),
        //     backgroundColor: Colors.red,
        //   ),
        // );
        _showErrorMessage('Error: ${e.toString()}');
      }
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(artisanPortfolioViewModelProvider);
    // final artisanData = state.artisanProfileData;

    return Scaffold(
      appBar: AppBar(title: const Text('Edit Profile'), elevation: 0),
      body:
          state.isLoading
              ? const Center(child: CircularProgressIndicator())
              : Stack(
                children: [
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildCoverImageSelector(),
                        _buildAvatarImageSelector(),
                        _buildTextField(
                          label: 'Full Name',
                          controller: _nameController,
                        ),
                        _buildTextField(
                          label: 'Location',
                          controller: _locationController,
                        ),
                        _buildCategoryDropdown(),
                        _buildSpecializationsSelector(),
                        _buildTextField(
                          label: 'About',
                          controller: _aboutController,
                          maxLines: 5,
                          useSemiBold: false,
                        ),
                        const SizedBox(height: 80), // Space for bottom button
                      ],
                    ),
                  ),
                  if (_isUploading)
                    Container(
                      color: Colors.black.withAlpha((0.5 * 255).round()),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                ],
              ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha((0.05 * 255).round()),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: PrimaryButton(
          text: 'Save Changes',
          onPressed: _saveProfile,
          isLoading: state.isSavingProfile,
          height: 50,
        ),
      ),
    );
  }
}
