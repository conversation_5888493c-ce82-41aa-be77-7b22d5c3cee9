import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/data/models/chat_models.dart';
import 'package:build_mate/data/services/chat_service.dart';
import 'package:build_mate/presentation/components/cards/message_card.dart';
import 'package:build_mate/presentation/screens/chat/chat_screen.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final chatServiceProvider = Provider((ref) => ChatService());

final conversationsProvider = FutureProvider.autoDispose<List<Conversation>>((
  ref,
) async {
  final chatService = ref.watch(chatServiceProvider);

  try {
    if (kDebugMode) {
      print('Starting to fetch client conversations');
    }

    // Get the current user's Supabase ID
    final supabase = Supabase.instance.client;
    final supabaseId = supabase.auth.currentUser?.id;

    if (kDebugMode) {
      print('Current Supabase user ID: $supabaseId');
    }

    if (supabaseId == null) {
      if (kDebugMode) {
        print('No authenticated user found');
      }
      return [];
    }

    // Get the client ID from the database using the Supabase ID
    try {
      final clientResponse =
          await supabase
              .from('clients')
              .select('id')
              .eq('supabase_id', supabaseId)
              .maybeSingle();

      if (kDebugMode) {
        print('Client response: $clientResponse');
      }

      if (clientResponse == null) {
        if (kDebugMode) {
          print('No client found for Supabase ID: $supabaseId');
        }
        return [];
      }

      final clientId = clientResponse['id'];
      if (kDebugMode) {
        print('Fetching conversations for client ID: $clientId');
      }

      // Set up realtime subscription for conversations table
      supabase
          .channel('public:conversations')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'conversations',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'client_id',
              value: clientId,
            ),
            callback: (payload) {
              if (kDebugMode) {
                print('Conversation change detected: ${payload.eventType}');
              }
              // Refresh the provider when a conversation changes
              ref.invalidateSelf();
            },
          )
          .subscribe();

      // Set up realtime subscription for messages table to update when new messages arrive
      supabase
          .channel('public:messages')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'messages',
            callback: (payload) {
              if (kDebugMode) {
                print('Message change detected: ${payload.eventType}');
              }
              // Refresh the provider when a message changes
              ref.invalidateSelf();
            },
          )
          .subscribe();

      // Get conversations for this client
      try {
        final conversations = await chatService.getConversations(
          true,
          clientId,
        );

        if (kDebugMode) {
          print(
            'Found ${conversations.length} conversations for client ID: $clientId',
          );
          for (final conversation in conversations) {
            print(
              'Conversation ID: ${conversation.id}, Artisan: ${conversation.artisan?.name}',
            );
          }
        }

        return conversations;
      } catch (e) {
        if (kDebugMode) {
          print('Error in chatService.getConversations: $e');
        }
        rethrow;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error querying clients table: $e');
      }
      rethrow;
    }
  } catch (e) {
    if (kDebugMode) {
      print('Top-level error in conversationsProvider: $e');
    }
    rethrow;
  }
});

class MessagesTabView extends ConsumerWidget {
  const MessagesTabView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final conversationsAsync = ref.watch(conversationsProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient:
                  themeMode == ThemeMode.light
                      ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          darkBlueColor,
                          darkBlueColor.withValues(alpha: 0.8),
                        ],
                      )
                      : null,
              color:
                  themeMode == ThemeMode.dark
                      ? customColors.surfaceVariant
                      : null,
            ),
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Messages',
                      style: MyTypography.SemiBold.copyWith(
                        fontSize: 24,
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white
                                : customColors.textPrimaryColor,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color:
                            themeMode == ThemeMode.light
                                ? Colors.white.withValues(alpha: 0.2)
                                : customColors.textPrimaryColor.withValues(
                                  alpha: 0.1,
                                ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.search,
                          color:
                              themeMode == ThemeMode.light
                                  ? Colors.white
                                  : customColors.textPrimaryColor,
                        ),
                        onPressed: () {
                          // TODO: Implement search functionality
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Debug button to manually trigger refresh
          if (kDebugMode)
            // ElevatedButton(
            //   onPressed: () {
            //     // ref.refresh(conversationsProvider);
            //     ref.invalidate(conversationsProvider);
            //     // final newValue = ref.read(conversationsProvider);
            //   },
            //   child: const Text('Refresh Conversations'),
            // ),
            Expanded(
              child: conversationsAsync.when(
                data: (conversations) {
                  if (kDebugMode) {
                    print('Rendering ${conversations.length} conversations');
                    for (final conversation in conversations) {
                      print(
                        'Rendering conversation: ${conversation.id}, Artisan: ${conversation.artisan?.name}',
                      );
                    }
                  }

                  if (conversations.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text('No conversations yet'),
                          const SizedBox(height: 16),
                          // Debug info
                          if (kDebugMode)
                            FutureBuilder(
                              future:
                                  Supabase
                                              .instance
                                              .client
                                              .auth
                                              .currentUser
                                              ?.id !=
                                          null
                                      ? Supabase.instance.client
                                          .from('clients')
                                          .select('id, name')
                                          .eq(
                                            'supabase_id',
                                            Supabase
                                                .instance
                                                .client
                                                .auth
                                                .currentUser!
                                                .id,
                                          )
                                          .maybeSingle()
                                      : Future.value(null),
                              builder: (context, snapshot) {
                                return Column(
                                  children: [
                                    Text(
                                      'Auth state: ${Supabase.instance.client.auth.currentSession != null ? 'Logged in' : 'Not logged in'}',
                                    ),
                                    Text(
                                      'User ID: ${Supabase.instance.client.auth.currentUser?.id ?? 'None'}',
                                    ),
                                    Text(
                                      'Client data: ${snapshot.data ?? 'Loading...'}',
                                    ),
                                  ],
                                );
                              },
                            ),
                        ],
                      ),
                    );
                  }

                  // Debug widget to show raw conversation data

                  return Column(
                    children: [
                      Expanded(
                        child:
                            conversations.isEmpty
                                ? _buildEmptyState(customColors, themeMode)
                                : ListView.separated(
                                  padding: const EdgeInsets.all(16),
                                  itemCount: conversations.length,
                                  separatorBuilder:
                                      (context, index) => Container(
                                        height: 8, // Increased spacing between cards
                                        color: Colors.transparent,
                                      ),
                                  itemBuilder: (context, index) {
                                    final conversation = conversations[index];
                                    final lastMessage =
                                        conversation.lastMessage;
                                    return MessageCard(
                                      senderName:
                                          conversation.artisan?.name ??
                                          'Unknown',
                                      message:
                                          lastMessage?.content ??
                                          'No messages yet',
                                      time:
                                          lastMessage != null
                                              ? _formatMessageTime(
                                                lastMessage.createdAt,
                                              )
                                              : '',
                                      avatarUrl:
                                          conversation.artisan?.avatar ??
                                          'assets/images/profile_pic.png',
                                      isUnread:
                                          lastMessage != null
                                              ? !lastMessage.isRead
                                              : false,
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) => ChatScreen(
                                                  conversationId:
                                                      conversation.id,
                                                  otherPartyName:
                                                      conversation
                                                          .artisan
                                                          ?.name ??
                                                      'Unknown',
                                                  otherPartyAvatar:
                                                      conversation
                                                          .artisan
                                                          ?.avatar,
                                                ),
                                          ),
                                        );
                                      },
                                    );
                                  },
                                ),
                      ),
                    ],
                  );
                },
                loading:
                    () => Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          themeMode == ThemeMode.light
                              ? darkBlueColor
                              : customColors.textPrimaryColor,
                        ),
                      ),
                    ),
                error:
                    (error, stack) => Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.6,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Unable to load conversations',
                            style: MyTypography.SemiBold.copyWith(
                              fontSize: 18,
                              color: customColors.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Please check your connection and try again',
                            style: MyTypography.Regular.copyWith(
                              fontSize: 14,
                              color: customColors.textPrimaryColor.withValues(
                                alpha: 0.7,
                              ),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () {
                              ref.invalidate(conversationsProvider);
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Retry'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  themeMode == ThemeMode.light
                                      ? darkBlueColor
                                      : customColors.primaryContainer,
                              foregroundColor:
                                  themeMode == ThemeMode.light
                                      ? Colors.white
                                      : customColors.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(CustomColors customColors, ThemeMode themeMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color:
                    themeMode == ThemeMode.light
                        ? darkBlueColor.withValues(alpha: 0.1)
                        : customColors.primaryContainer.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                size: 64,
                color:
                    themeMode == ThemeMode.light
                        ? darkBlueColor
                        : customColors.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No conversations yet',
              style: MyTypography.SemiBold.copyWith(
                fontSize: 20,
                color: customColors.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Start a conversation with an artisan\nby posting a job or browsing services',
              style: MyTypography.Regular.copyWith(
                fontSize: 16,
                color: customColors.textPrimaryColor.withValues(alpha: 0.7),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to post job or browse services
              },
              icon: const Icon(Icons.add),
              label: const Text('Post a Job'),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    themeMode == ThemeMode.light
                        ? darkBlueColor
                        : customColors.primaryContainer,
                foregroundColor:
                    themeMode == ThemeMode.light
                        ? Colors.white
                        : customColors.textPrimaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatMessageTime(String timestamp) {
    final messageTime = DateTime.parse(timestamp).toLocal();
    final now = DateTime.now();
    final difference = now.difference(messageTime);

    if (difference.inDays == 0) {
      // Today, show time
      return DateFormat('hh:mm a').format(messageTime);
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // Within a week
      return DateFormat('EEEE').format(messageTime); // Day name
    } else {
      // Older
      return DateFormat('MMM d').format(messageTime); // Month and day
    }
  }
}
