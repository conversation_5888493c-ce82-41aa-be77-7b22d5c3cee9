import 'package:build_mate/presentation/components/helper_widgets/spacing_widgets.dart';
import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/screens/home/<USER>/bottom_nav_bar.dart';
import 'package:build_mate/presentation/view_models/user/client_profile_tab_view_model.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProfileTabView extends ConsumerWidget {
  const ProfileTabView({super.key});

  Widget _buildSettingsGroup({
    required String title,
    required List<Widget> children,
    required BuildContext context,
    required WidgetRef ref,
  }) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title.toUpperCase(),
            style: MyTypography.SemiBold.copyWith(
              color: customColors.textPrimaryColor.withValues(alpha: 0.6),
              fontSize: 13,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border:
                themeMode == ThemeMode.dark
                    ? Border.all(
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.1,
                      ),
                      width: 1,
                    )
                    : null,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(children: children),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
    required WidgetRef ref,
  }) {
    final customColors = ref.watch(customColorsProvider);

    return ListTile(
      leading: Icon(icon, color: iconColor ?? orangeColor, size: 22),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color: customColors.textPrimaryColor,
        ),
      ),
      trailing:
          trailing ??
          Icon(
            Icons.chevron_right,
            color: customColors.textPrimaryColor.withValues(alpha: 0.4),
            size: 20,
          ),
      onTap: onTap,
    );
  }

  String _getThemeModeText(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showThemeDialog(
    BuildContext context,
    ThemeModeNotifier themeModeNotifier,
    ThemeMode currentTheme,
    WidgetRef ref,
  ) {
    final customColors = ref.watch(customColorsProvider);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: Text(
            'Choose Theme',
            style: MyTypography.SemiBold.copyWith(
              fontSize: 18,
              color: customColors.textPrimaryColor,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildThemeOption(
                context,
                'Light',
                Icons.light_mode,
                ThemeMode.light,
                currentTheme,
                themeModeNotifier,
                ref,
              ),
              _buildThemeOption(
                context,
                'Dark',
                Icons.dark_mode,
                ThemeMode.dark,
                currentTheme,
                themeModeNotifier,
                ref,
              ),
              _buildThemeOption(
                context,
                'System',
                Icons.settings_system_daydream,
                ThemeMode.system,
                currentTheme,
                themeModeNotifier,
                ref,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String title,
    IconData icon,
    ThemeMode themeMode,
    ThemeMode currentTheme,
    ThemeModeNotifier themeModeNotifier,
    WidgetRef ref,
  ) {
    final customColors = ref.watch(customColorsProvider);
    final isSelected = currentTheme == themeMode;

    return ListTile(
      leading: Icon(
        icon,
        color:
            isSelected
                ? orangeColor
                : customColors.textPrimaryColor.withValues(alpha: 0.6),
      ),
      title: Text(
        title,
        style: MyTypography.Regular.copyWith(
          fontSize: 16,
          color: isSelected ? orangeColor : customColors.textPrimaryColor,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? Icon(Icons.check, color: orangeColor, size: 20) : null,
      onTap: () {
        themeModeNotifier.setThemeMode(themeMode);
        Navigator.of(context).pop();
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(clientProfileTabViewModelProvider);
    final notifier = ref.watch(clientProfileTabViewModelProvider.notifier);
    final themeMode = ref.watch(themeModeProvider);
    final themeModeNotifier = ref.read(themeModeProvider.notifier);
    final customColors = ref.watch(customColorsProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            backgroundColor:
                themeMode == ThemeMode.light
                    ? darkBlueColor
                    : customColors.surfaceVariant,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color:
                    themeMode == ThemeMode.light
                        ? Colors.white
                        : customColors.textPrimaryColor,
              ),
              onPressed: () => context.pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient:
                      themeMode == ThemeMode.light
                          ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              darkBlueColor,
                              darkBlueColor.withValues(alpha: 0.8),
                            ],
                          )
                          : null,
                  color:
                      themeMode == ThemeMode.dark
                          ? customColors.surfaceVariant
                          : null,
                ),
                child: Stack(
                  children: [
                    Positioned(
                      bottom: 20,
                      left: 20,
                      right: 20,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: ClipOval(
                              child: Image.network(
                                state.profileUrl,
                                fit: BoxFit.cover,
                                loadingBuilder: (
                                  context,
                                  child,
                                  loadingProgress,
                                ) {
                                  if (loadingProgress == null) return child;
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color:
                                          themeMode == ThemeMode.light
                                              ? Colors.grey[200]
                                              : customColors.surfaceVariant,
                                      shape: BoxShape.circle,
                                    ),
                                    child: SizedBox(
                                      width: 32,
                                      height: 32,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 3,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              themeMode == ThemeMode.light
                                                  ? darkBlueColor
                                                  : customColors
                                                      .textPrimaryColor,
                                            ),
                                      ),
                                    ),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color:
                                          themeMode == ThemeMode.light
                                              ? Colors.grey[300]
                                              : customColors.surfaceVariant,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.person,
                                      color: customColors.textPrimaryColor
                                          .withValues(alpha: 0.6),
                                      size: 40,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  state.username,
                                  style: MyTypography.SemiBold.copyWith(
                                    fontSize: 24,
                                    color:
                                        themeMode == ThemeMode.light
                                            ? Colors.white
                                            : customColors.textPrimaryColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // Container(
                                //   padding: const EdgeInsets.symmetric(
                                //     horizontal: 8,
                                //     vertical: 4,
                                //   ),
                                //   decoration: BoxDecoration(
                                //     color: Colors.green.withAlpha(
                                //       51,
                                //     ), // 0.2 opacity
                                //     borderRadius: BorderRadius.circular(12),
                                //   ),
                                //   child: Text(
                                //     'Online',
                                //     style: MyTypography.Medium.copyWith(
                                //       fontSize: 12,
                                //       color: Colors.green,
                                //     ),
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 24),
                _buildSettingsGroup(
                  title: 'ACCOUNT',
                  context: context,
                  ref: ref,
                  children: [
                    _buildSettingsTile(
                      icon: Icons.verified_user,
                      title: 'Profile',
                      ref: ref,
                      onTap: () {
                        context.pushNamed(
                          RouteConstants.CLIENT_SETTINGS_SCREEN,
                        );
                      },
                    ),
                    // Divider(height: 1, color: Colors.grey[200]),
                    // _buildSettingsTile(
                    //   icon: Icons.download_outlined,
                    //   title: 'Downloads',
                    //   onTap: () {},
                    // ),
                  ],
                ),
                _buildSettingsGroup(
                  title: 'PREFERENCES',
                  context: context,
                  ref: ref,
                  children: [
                    _buildSettingsTile(
                      icon: Icons.language,
                      title: 'Language',
                      ref: ref,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'English',
                            style: MyTypography.Regular.copyWith(
                              color: customColors.textPrimaryColor.withValues(
                                alpha: 0.6,
                              ),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.4,
                            ),
                            size: 20,
                          ),
                        ],
                      ),
                      onTap: () {},
                    ),
                    Divider(
                      height: 1,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.1,
                      ),
                    ),
                    _buildSettingsTile(
                      icon: Icons.palette_outlined,
                      title: 'Theme',
                      ref: ref,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _getThemeModeText(themeMode),
                            style: MyTypography.Regular.copyWith(
                              color: customColors.textPrimaryColor.withValues(
                                alpha: 0.6,
                              ),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.chevron_right,
                            color: customColors.textPrimaryColor.withValues(
                              alpha: 0.4,
                            ),
                            size: 20,
                          ),
                        ],
                      ),
                      onTap:
                          () => _showThemeDialog(
                            context,
                            themeModeNotifier,
                            themeMode,
                            ref,
                          ),
                    ),
                    vSpace(8),
                
                    // _buildSettingsTile(
                    //   icon: Icons.wifi,
                    //   title: 'Only Download via Wi-Fi',
                    //   trailing: Switch(
                    //     value: true,
                    //     onChanged: (value) {},
                    //     activeColor: Colors.blue,
                    //   ),
                    // ),
                    // Divider(height: 1, color: Colors.grey[200]),
                    // _buildSettingsTile(
                    //   icon: Icons.play_circle_outline,
                    //   title: 'Play in Background',
                    //   trailing: Switch(
                    //     value: false,
                    //     onChanged: (value) {},
                    //     activeColor: Colors.blue,
                    //   ),
                    // ),
                  ],
                ),

                _buildSettingsGroup(
                  title: 'ACCOUNT ACTIONS',
                  context: context,
                  ref: ref,
                  children: [
                    _buildSettingsTile(
                      icon: Icons.logout,
                      title: 'Logout',
                      ref: ref,
                      iconColor: Colors.red,
                      trailing: const Icon(
                        Icons.chevron_right,
                        color: Colors.red,
                        size: 20,
                      ),
                      onTap: () async {
                        // Show confirmation dialog
                        final shouldLogout = await showDialog<bool>(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                backgroundColor: Theme.of(context).cardColor,
                                title: Text(
                                  'Logout',
                                  style: MyTypography.SemiBold.copyWith(
                                    color: customColors.textPrimaryColor,
                                  ),
                                ),
                                content: Text(
                                  'Are you sure you want to logout?',
                                  style: MyTypography.Regular.copyWith(
                                    color: customColors.textPrimaryColor,
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(false),
                                    child: Text(
                                      'Cancel',
                                      style: MyTypography.Medium.copyWith(
                                        color: customColors.textPrimaryColor,
                                      ),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(true),
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.red,
                                    ),
                                    child: const Text('Logout'),
                                  ),
                                ],
                              ),
                        );

                        if (shouldLogout == true) {
                          await notifier.logout();
                          if (context.mounted) {
                            // Set bottom nav bar index to zero after logout
                            final container = ProviderScope.containerOf(
                              context,
                              listen: false,
                            );
                            container
                                .read(homeNavigationIndexProvider.notifier)
                                .state = 0;
                            context.goNamed(
                              RouteConstants.ROLE_SELECTION_SCREEN,
                            );
                          }
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
