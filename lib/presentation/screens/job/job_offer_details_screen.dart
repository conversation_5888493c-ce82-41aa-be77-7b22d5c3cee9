import 'package:build_mate/data/models/posted_job_model.dart';
import 'package:build_mate/presentation/components/dismissible_image_viewer.dart';
import 'package:build_mate/presentation/view_models/artisan/job_posted_view_model.dart';
import 'package:build_mate/presentation/view_models/artisan/job_posted_details_view_model.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';


class JobOfferDetailsScreen extends ConsumerStatefulWidget {
  const JobOfferDetailsScreen({super.key});

  @override
  ConsumerState<JobOfferDetailsScreen> createState() =>
      _JobOfferDetailsScreenState();
}

class _JobOfferDetailsScreenState extends ConsumerState<JobOfferDetailsScreen> {
  bool _isBidding = false;
  bool _isCanceling = false;

  @override
  void initState() {
    super.initState();
    // Set up post-frame callback to refresh data after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Force refresh jobs data when screen opens
      final jobPostedViewModel = ref.read(jobPostedViewModelProvider.notifier);
      jobPostedViewModel.fetchAvailableJobsForArtisan();

      // Set up a listener for the jobs stream
      jobPostedViewModel.setupRealtimeSubscription();
    });
  }

  @override
  void dispose() {
    // Clean up if needed
    super.dispose();
  }

  String _formatDate(DateTime date) {
    final List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  Widget _buildImageGrid(List<String> images) {
    // Use placeholder images if no images are provided
    final List<String> displayImages =
        images.isEmpty
            ? [
              'assets/images/grid_1.jpeg',
              'assets/images/grid_2.jpeg',
              'assets/images/grid_3.jpg',
              'assets/images/grid_4.jpeg',
            ]
            : images;

    // Limit to maximum 4 images
    final List<String> limitedImages =
        displayImages.length > 4 ? displayImages.sublist(0, 4) : displayImages;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: limitedImages.length,
      itemBuilder: (context, index) {
        final String imageUrl = limitedImages[index];
        final bool isNetworkImage = imageUrl.startsWith('http');

        return GestureDetector(
          onTap: () => _showImageViewer(context, limitedImages, index),
          child: Hero(
            tag: 'job_image_$index',
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child:
                  isNetworkImage
                      ? Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          if (kDebugMode) {
                            print('Error loading network image: $error');
                          }
                          return Container(
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.broken_image,
                              color: Colors.grey[500],
                              size: 40,
                            ),
                          );
                        },
                      )
                      : Image.asset(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          if (kDebugMode) {
                            print('Error loading asset image: $error');
                          }
                          return Container(
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.broken_image,
                              color: Colors.grey[500],
                              size: 40,
                            ),
                          );
                        },
                      ),
            ),
          ),
        );
      },
    );
  }

  void _showImageViewer(
    BuildContext context,
    List<String> images,
    int initialIndex,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DismissibleImageViewer(
            images: images,
            initialIndex: initialIndex,
          ),
    );
  }

  void _showBidConfirmationDialog(BuildContext context) {
    final scaffoldMessengerState = ScaffoldMessenger.of(context);
    final jobPostedViewModel = ref.read(jobPostedViewModelProvider.notifier);
    final jobPostedState = ref.read(jobPostedViewModelProvider);

    if (kDebugMode) {
      print('Showing bid confirmation dialog');
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Express Interest',
              style: MyTypography.SemiBold.copyWith(fontSize: 18),
            ),
            content: Text(
              'Are you interested in this job? The client will be notified of your interest and may contact you for further details.',
              style: MyTypography.Regular.copyWith(
                color: Colors.black87,
                fontSize: 14,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: MyTypography.Medium.copyWith(color: Colors.grey[700]),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (kDebugMode) {
                    print('Confirm button pressed');
                  }

                  // Close dialog first
                  Navigator.pop(context);

                  // Set bidding state to true to show loading indicator
                  setState(() {
                    _isBidding = true;
                  });

                  try {
                    if (kDebugMode) {
                      print('Calling bid method');
                    }

                    // Use the jobPostedViewModel
                    await jobPostedViewModel.bid(
                      jobPostedState.selectedJob ??
                          PostedJobModel(
                            id: 0,
                            title: '',
                            clientName: '',
                            description: '',
                            budget: 0.0,
                            categories: [],
                            images: [],
                            status: '',
                            serviceDate: DateTime.now(),
                            postDate: DateTime.now(),
                            bids: [],
                            hasMyBid: false,
                          ),
                    );

                    if (kDebugMode) {
                      print('bid completed successfully');
                    }

                    // Show success message
                    if (mounted) {
                      // ScaffoldMessenger.of(scaffoldMessengerState).showSnackBar(
                      //   const SnackBar(
                      //     content: Text(
                      //       'You have expressed interest in this job!',
                      //     ),
                      //     backgroundColor: Colors.green,
                      //   ),
                      // );

                      _showSuccessMessage(
                        scaffoldMessengerState,
                        'You have expressed interest in this job!',
                      );
                    }
                  } catch (e) {
                    if (kDebugMode) {
                      print('Error in bid: $e');
                    }

                    // Show error message if bid fails
                    if (mounted) {
                      // ScaffoldMessenger.of(context).showSnackBar(
                      //   SnackBar(
                      //     content: Text(
                      //       'Failed to express interest: ${e.toString()}',
                      //     ),
                      //     backgroundColor: Colors.red,
                      //   ),
                      // );
                      _showErrorMessage(
                        scaffoldMessengerState,
                        'Failed to express interest: ${e.toString()}',
                      );
                    }
                  } finally {
                    // Reset bidding state
                    if (mounted) {
                      setState(() {
                        _isBidding = false;
                      });
                    }
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: orangeColor),
                child: Text(
                  'Confirm Interest',
                  style: MyTypography.Medium.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _showSuccessMessage(ScaffoldMessengerState messenger, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorMessage(ScaffoldMessengerState messenger, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showCancelBidConfirmationDialog(BuildContext context) {
    // Capture all necessary references before showing the dialog
    final jobPostedViewModel = ref.read(
      jobPostedDetailsViewModelProvider.notifier,
    );
    final jobPostedState = ref.read(jobPostedDetailsViewModelProvider);
    final job = jobPostedState.job;

    // Store context reference before showing dialog
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    // Check if a job is selected
    if (job == null) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('No job selected. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'Cancel Interest',
              style: MyTypography.SemiBold.copyWith(fontSize: 18),
            ),
            content: Text(
              'Are you sure you want to cancel your interest in this job?',
              style: MyTypography.Regular.copyWith(
                color: Colors.black87,
                fontSize: 14,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => navigator.pop(),
                child: Text(
                  'No, Keep Interest',
                  style: MyTypography.Medium.copyWith(color: Colors.grey[700]),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Close dialog first
                  navigator.pop();

                  // Set canceling state to true to show loading indicator
                  if (mounted) {
                    setState(() {
                      _isCanceling = true;
                    });
                  }

                  try {
                    // Use the jobPostedViewModel
                    await jobPostedViewModel.cancelBid();

                    // Show success message
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(
                          content: Text('Your interest has been canceled.'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    // Show error message if cancelBid fails
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            'Failed to cancel interest: ${e.toString()}',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } finally {
                    // Reset canceling state
                    if (mounted) {
                      setState(() {
                        _isCanceling = false;
                      });
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                ),
                child: Text(
                  'Yes, Cancel Interest',
                  style: MyTypography.Medium.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final jobPostedState = ref.watch(jobPostedViewModelProvider);
    final job = jobPostedState.selectedJob;

    // If no job is selected, show a loading indicator or error message
    if (job == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final clientName = job.clientName;
    final clientImageUrl =
        job.clientAvatar ?? 'assets/images/avatar_placeholder.png';
    final jobTitle = job.title;
    final serviceTags = job.categories;
    final location = "Harare, Zimbabwe"; // This should come from the job model
    final description = job.description;
    final serviceDate = job.serviceDate;
    // final serviceTimes = DateFormat('MMM dd, yyyy').format(job.serviceDate);
    final jobImages = job.images;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Dark Blue Header Section
          Container(
            color: const Color(0xFF0A1B3D),
            child: SafeArea(
              bottom: false,
              child: Column(
                children: [
                  // AppBar
                  AppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => context.pop(),
                    ),
                    title: Text(
                      'New job offer',
                      style: MyTypography.SemiBold.copyWith(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                  ),

                  // Client Profile Section
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.grey[300],
                          backgroundImage:
                              clientImageUrl.startsWith('http')
                                  ? NetworkImage(clientImageUrl)
                                  : AssetImage(clientImageUrl) as ImageProvider,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          clientName,
                          style: MyTypography.SemiBold.copyWith(
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // White Content Section
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Job Title
                        Text(
                          jobTitle,
                          style: MyTypography.Bold.copyWith(
                            color: Colors.black87,
                            fontSize: 24,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Service Tags
                        if (serviceTags.isNotEmpty) ...[
                          SizedBox(
                            height: 32,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: serviceTags.length,
                              itemBuilder: (context, index) {
                                // Create a unique color for each tag based on its index
                                final tagColors = [
                                  const Color(0xFFE3F2FD), // Light blue
                                  const Color(0xFFE8F5E9), // Light green
                                  const Color(0xFFFFF3E0), // Light orange
                                ];
                                final textColors = [
                                  const Color(0xFF1565C0), // Dark blue
                                  const Color(0xFF2E7D32), // Dark green
                                  const Color(0xFFE65100), // Dark orange
                                ];

                                // Use modulo to cycle through colors if there are more than 3 tags
                                final colorIndex = index % tagColors.length;

                                return Padding(
                                  padding: EdgeInsets.only(
                                    right:
                                        index != serviceTags.length - 1 ? 8 : 0,
                                  ),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: tagColors[colorIndex],
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(
                                            (0.05 * 255).round(),
                                          ),
                                          blurRadius: 2,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      serviceTags[index],
                                      style: MyTypography.Medium.copyWith(
                                        fontSize: 12,
                                        color: textColors[colorIndex],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        // Location
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              color: Colors.blue[400],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: location,
                                    style: MyTypography.Medium.copyWith(
                                      color: Colors.blue[400],
                                      fontSize: 16,
                                    ),
                                  ),
                                  TextSpan(
                                    text: ' (2.5 km)',
                                    style: MyTypography.Light.copyWith(
                                      color: Colors.grey[600],
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // New Offer Tag and Budget
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.teal,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 500),
                                transitionBuilder: (
                                  Widget child,
                                  Animation<double> animation,
                                ) {
                                  return FadeTransition(
                                    opacity: animation,
                                    child: ScaleTransition(
                                      scale: animation,
                                      child: child,
                                    ),
                                  );
                                },
                                child: Text(
                                  'New offer (${job.bids.length} bids)',
                                  key: ValueKey<String>(
                                    "bids-${job.bids.length}",
                                  ), // Key changes when bid count changes
                                  style: MyTypography.Medium.copyWith(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFF4C02), // Orange color
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                'Budget: \$${job.budget.toStringAsFixed(0)}',
                                style: MyTypography.Medium.copyWith(
                                  color: Colors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                        // Budget explanation text
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 4.0,
                            bottom: 16.0,
                          ),
                          child: Text(
                            'Estimated budget for this service',
                            style: MyTypography.Regular.copyWith(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ),

                        // Service Details Section
                        Text(
                          'Service needed by:',
                          style: MyTypography.SemiBold.copyWith(
                            color: Colors.black87,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 16,
                                color: Colors.blue[400],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _formatDate(serviceDate),
                                style: MyTypography.Medium.copyWith(
                                  color: Colors.black87,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Service Description
                        Text(
                          'Service description',
                          style: MyTypography.SemiBold.copyWith(
                            color: Colors.black87,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          description,
                          style: MyTypography.Medium.copyWith(
                            color: Colors.black54,
                            fontSize: 14,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Job Images Section (conditionally displayed)
                        if (jobImages.isNotEmpty) ...[
                          Text(
                            'Job Images',
                            style: MyTypography.SemiBold.copyWith(
                              color: Colors.black87,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(height: 1),
                          _buildImageGrid(jobImages),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Bottom Action Buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha((0.05 * 255).round()),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        _isBidding || _isCanceling ? null : () => context.pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      disabledBackgroundColor: Colors.grey[200]?.withAlpha(
                        (0.7 * 255).round(),
                      ),
                      disabledForegroundColor: Colors.black87.withAlpha(
                        (0.5 * 255).round(),
                      ),
                    ),
                    child: Text('Back', style: MyTypography.SemiBold),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child:
                      job.hasMyBid
                          ? ElevatedButton(
                            onPressed:
                                _isCanceling
                                    ? null
                                    : () => _showCancelBidConfirmationDialog(
                                      context,
                                    ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[400],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              disabledBackgroundColor: Colors.red[400]
                                  ?.withAlpha((0.7 * 255).round()),
                            ),
                            child:
                                _isCanceling
                                    ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Canceling...',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    )
                                    : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.cancel_outlined),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Cancel Interest',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                          )
                          : ElevatedButton(
                            onPressed:
                                _isBidding
                                    ? null
                                    : () => _showBidConfirmationDialog(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: orangeColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              disabledBackgroundColor: orangeColor.withAlpha(
                                (0.7 * 255).round(),
                              ),
                            ),
                            child:
                                _isBidding
                                    ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Processing...',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    )
                                    : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.handshake_outlined),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Express Interest',
                                          style: MyTypography.SemiBold.copyWith(
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                          ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
