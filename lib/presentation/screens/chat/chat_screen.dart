import 'dart:async';

import 'package:build_mate/presentation/screens/home/<USER>/tabs/messages_tab_view.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:build_mate/data/models/chat_models.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final messagesProvider = FutureProvider.family<List<Message>, int>((
  ref,
  conversationId,
) async {
  final chatService = ref.watch(chatServiceProvider);
  return chatService.getMessages(conversationId);
});

final chatControllerProvider = StateNotifierProvider.family<
  ChatController,
  AsyncValue<List<Message>>,
  int
>((ref, conversationId) => ChatController(ref, conversationId));

// Provider to track if a message is currently being sent
final isSendingMessageProvider = StateProvider<bool>((ref) => false);

// Add a state provider to track which messages are being deleted
final isDeletingMessageProvider = StateProvider.family<bool, int>(
  (ref, messageId) => false,
);

// Add state providers at the top of the file with other providers
final isTypingProvider = StateProvider.family<bool, int>(
  (ref, conversationId) => false,
);
final otherPartyTypingProvider = StateProvider.family<bool, int>(
  (ref, conversationId) => false,
);

class ChatController extends StateNotifier<AsyncValue<List<Message>>> {
  final Ref _ref;
  final int conversationId;
  RealtimeChannel? _channel;
  RealtimeChannel? _presenceChannel;
  Timer? _typingTimer;
  bool _isTyping = false;
  bool _presenceReady = false;

  ChatController(this._ref, this.conversationId)
    : super(const AsyncValue.loading()) {
    _loadMessages();
    _subscribeToMessages();
    _setupPresence();
  }

  // Set up presence for typing indicators
  void _setupPresence() async {
    final supabase = Supabase.instance.client;
    final channelName = 'presence:$conversationId';

    // Determine if current user is client or artisan
    final isClient = await _isCurrentUserClient();
    final userType = isClient ? 'client' : 'artisan';

    if (kDebugMode) {
      print('[$conversationId] Setting up presence for $userType');
    }

    // Create a presence channel
    _presenceChannel = supabase.channel(channelName);

    // Track presence changes using the newer API
    _presenceChannel!
        .onPresenceSync((payload) {
          if (kDebugMode) {
            print(
              '[$conversationId] Presence sync: ${_presenceChannel!.presenceState()}',
            );
          }
          _updateTypingStatus(_presenceChannel!.presenceState());
        })
        .onPresenceJoin((payload) {
          if (kDebugMode) {
            print('[$conversationId] Presence join: $payload');
          }
          _handlePresenceJoin(payload);
        })
        .onPresenceLeave((payload) {
          if (kDebugMode) {
            print('[$conversationId] Presence leave: $payload');
          }
          _handlePresenceLeave(payload);
        })
        .subscribe((status, error) async {
          if (error != null) {
            if (kDebugMode) {
              print('[$conversationId] Error subscribing to presence: $error');
            }
            _presenceReady = false;
            return;
          }

          if (kDebugMode) {
            print('[$conversationId] Presence channel status: $status');
          }

          // Only track presence after successful subscription
          try {
            // Track the current user's presence
            await _presenceChannel!.track({
              'user_type': userType,
              'conversation_id': conversationId,
              'typing': false,
              'online': true,
              'last_seen': DateTime.now().toIso8601String(),
            });

            // Mark presence as ready
            _presenceReady = true;

            if (kDebugMode) {
              print(
                '[$conversationId] Presence tracking started for $userType',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              print('[$conversationId] Error tracking presence: $e');
            }
            _presenceReady = false;
          }
        });
  }

  // Handle presence join event
  void _handlePresenceJoin(RealtimePresenceJoinPayload payload) {
    try {
      if (kDebugMode) {
        print('[$conversationId] Processing presence join: $payload');
      }

      // Process each new presence
      for (final presence in payload.newPresences) {
        final presenceData = presence.payload; // Access the payload directly
        if (presenceData['user_type'] != null) {
          _checkOtherPartyTyping(presenceData);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error handling presence join: $e');
      }
    }
  }

  // Handle presence leave event
  Future<void> _handlePresenceLeave(
    RealtimePresenceLeavePayload payload,
  ) async {
    try {
      if (kDebugMode) {
        print('[$conversationId] Processing presence leave: $payload');
      }

      // Process each leaving presence
      for (final presence in payload.leftPresences) {
        final presenceData = presence.payload;

        // Get the user type of the leaving presence
        final String leavingUserType = presenceData['user_type'];
        final isClient = await _isCurrentUserClient();
        final String myUserType = isClient ? 'client' : 'artisan';

        // Update typing status if the other party is leaving
        if (leavingUserType != myUserType) {
          if (kDebugMode) {
            print(
              '[$conversationId] Other party ($leavingUserType) left. Setting typing to false.',
            );
          }
          _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
              false;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error handling presence leave: $e');
      }
    }
  }

  // Check if the other party is typing based on a single presence payload
  void _checkOtherPartyTyping(dynamic payload) async {
    try {
      final isClient = await _isCurrentUserClient();
      final otherPartyType = isClient ? 'artisan' : 'client';

      if (payload['user_type'] == otherPartyType && payload['typing'] == true) {
        _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
            true;
      } else if (payload['user_type'] == otherPartyType &&
          payload['typing'] == false) {
        _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
            false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error checking other party typing: $e');
      }
    }
  }

  // Update typing status based on presence state
  void _updateTypingStatus(dynamic presenceState) async {
    try {
      if (presenceState == null) return;

      final isClient = await _isCurrentUserClient();
      final otherPartyType = isClient ? 'artisan' : 'client';

      // Check for other party's typing status
      bool otherPartyTyping = false;

      // Handle the new presence state structure
      if (presenceState is List) {
        // New structure: List of PresenceState objects
        for (final state in presenceState) {
          if (state.presences != null) {
            for (final presence in state.presences) {
              final payload = presence.payload;
              if (payload != null &&
                  payload['user_type'] == otherPartyType &&
                  payload['typing'] == true) {
                otherPartyTyping = true;
                break;
              }
            }
          }
        }
      } else {
        // Old structure: Map of presences
        presenceState.forEach((key, presences) {
          for (final presence in presences) {
            if (presence['user_type'] == otherPartyType &&
                presence['typing'] == true) {
              otherPartyTyping = true;
              break;
            }
          }
        });
      }

      // Update the typing status provider
      _ref.read(otherPartyTypingProvider(conversationId).notifier).state =
          otherPartyTyping;
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error updating typing status: $e');
      }
    }
  }

  // Update typing status
  void updateTypingStatus(bool isTyping) async {
    if (_isTyping == isTyping) return; // No change

    _isTyping = isTyping;
    _ref.read(isTypingProvider(conversationId).notifier).state = isTyping;

    // Only update presence if the channel is ready
    if (!_presenceReady || _presenceChannel == null) {
      if (kDebugMode) {
        print(
          '[$conversationId] Presence channel not ready, skipping typing update',
        );
      }
      return;
    }

    try {
      final isClient = await _isCurrentUserClient();

      // Update presence with typing status
      await _presenceChannel!.track({
        'user_type': isClient ? 'client' : 'artisan',
        'conversation_id': conversationId,
        'typing': isTyping,
        'online': true,
        'last_seen': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('[$conversationId] Updated typing status: $isTyping');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$conversationId] Error updating typing status: $e');
      }
    }
  }

  // Start typing indicator with debounce
  void startTyping() {
    // Cancel existing timer
    _typingTimer?.cancel();

    // Update typing status to true
    updateTypingStatus(true);

    // Set timer to stop typing indicator after 2 seconds of inactivity
    _typingTimer = Timer(const Duration(seconds: 2), () {
      updateTypingStatus(false);
    });
  }

  // Stop typing indicator
  void stopTyping() {
    _typingTimer?.cancel();
    updateTypingStatus(false);
  }

  Future<void> _loadMessages() async {
    try {
      final chatService = _ref.read(chatServiceProvider);
      final messages = await chatService.getMessages(conversationId);
      if (kDebugMode) {
        print(
          'Loaded ${messages.length} messages for conversation $conversationId',
        );
      }

      // Mark messages as read
      final isClient = await _isCurrentUserClient();
      await chatService.markMessagesAsRead(
        conversationId,
        isClient ? 'client' : 'artisan',
      );

      state = AsyncValue.data(messages);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading messages: $e');
      }
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  void _subscribeToMessages() {
    if (kDebugMode) {
      print(
        'Setting up realtime subscription for conversation $conversationId',
      );
    }
    final supabase = Supabase.instance.client;

    // Create a unique channel name for this conversation
    final channelName = 'can$conversationId';
    if (kDebugMode) {
      print('Subscribing to channel: $channelName');
    }

    // Subscribe to the realtime changes on the messages table for this conversation
    _channel = supabase
        .channel(channelName)
        // Listen to PostgreSQL changes for inserts and updates
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'messages',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'conversation_id',
            value: conversationId.toString(),
          ),
          callback: (payload) {
            if (kDebugMode) {
              print(
                '[$channelName] Received PostgreSQL change event: ${payload.eventType}',
              );
            }
            if (kDebugMode) {
              print('[$channelName] Payload: ${payload.toString()}');
            }

            // Handle different event types
            if (payload.eventType == PostgresChangeEvent.insert &&
                payload.newRecord.isNotEmpty) {
              try {
                final newMessage = Message.fromJson(payload.newRecord);
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing new message: ${newMessage.id}',
                  );
                }
                _handleNewMessage(newMessage);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing new message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: ${payload.newRecord}');
                }
              }
            } else if (payload.eventType == PostgresChangeEvent.update &&
                payload.newRecord.isNotEmpty &&
                payload.oldRecord.isNotEmpty) {
              try {
                final updatedMessage = Message.fromJson(payload.newRecord);
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing updated message: ${updatedMessage.id}',
                  );
                }
                _handleUpdatedMessage(updatedMessage);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing updated message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: ${payload.newRecord}');
                }
              }
            } else if (payload.eventType == PostgresChangeEvent.delete &&
                payload.oldRecord.isNotEmpty) {
              try {
                final deletedMessageId = payload.oldRecord['id'];
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing deleted message from Postgres: $deletedMessageId',
                  );
                }
                handleDeletedMessage(deletedMessageId);
              } catch (e) {
                if (kDebugMode) {
                  print('[$channelName] Error processing deleted message: $e');
                }
                if (kDebugMode) {
                  print('[$channelName] Payload: ${payload.oldRecord}');
                }
              }
            }
          },
        )
        // Add this broadcast listener for deletion events
        .onBroadcast(
          event: 'shout',
          callback: (payload) {
            if (kDebugMode) {
              print('[$channelName] Received shout broadcast: $payload');
            }

            try {
              // Check if this is a delete message event
              if (payload['type'] == 'delete_message' &&
                  payload['message_id'] != null) {
                final messageId = payload['message_id'];
                if (kDebugMode) {
                  print(
                    '[$channelName] Processing deleted message from shout: $messageId',
                  );
                }

                // Force UI update on main thread
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  final controller = _ref.read(
                    chatControllerProvider(conversationId).notifier,
                  );
                  controller.handleDeletedMessage(messageId);
                });
              }
            } catch (e) {
              if (kDebugMode) {
                print('[$channelName] Error processing shout broadcast: $e');
              }
              if (kDebugMode) {
                print('[$channelName] Payload: $payload');
              }
            }
          },
        )
        .subscribe((status, error) {
          if (error != null) {
            if (kDebugMode) {
              print('[$channelName] Error subscribing to messages: $error');
            }
          } else {
            if (kDebugMode) {
              print(
                '[$channelName] Successfully subscribed to messages channel: $status',
              );
            }
            if (kDebugMode) {
              print(
                '[$channelName] Channel is now active and listening for events',
              );
            }
          }
        });
  }

  // Extract message handling logic to a separate method
  void _handleNewMessage(Message newMessage) {
    if (state.hasValue) {
      if (kDebugMode) {
        print('Handling new message: ${newMessage.id}');
      }

      // Check if message already exists to avoid duplicates
      final messageExists = state.value!.any(
        (msg) =>
            msg.id == newMessage.id ||
            (msg.id < 0 &&
                msg.content == newMessage.content &&
                msg.senderType == newMessage.senderType &&
                msg.senderId == newMessage.senderId),
      );

      if (!messageExists) {
        if (kDebugMode) {
          print('Message is new, adding to state');
        }
        final updatedMessages = [...state.value!];

        // Remove any temporary messages with the same content
        updatedMessages.removeWhere(
          (msg) =>
              msg.id < 0 &&
              msg.content == newMessage.content &&
              msg.senderType == newMessage.senderType &&
              msg.senderId == newMessage.senderId,
        );

        // Add the new message
        updatedMessages.add(newMessage);

        // Sort messages by creation time to ensure correct order
        updatedMessages.sort(
          (a, b) => DateTime.parse(
            a.createdAt,
          ).compareTo(DateTime.parse(b.createdAt)),
        );

        state = AsyncValue.data(updatedMessages);
        if (kDebugMode) {
          print(
            'State updated with new message. Total messages: ${updatedMessages.length}',
          );
        }

        // Mark message as read if it's from the other party
        final chatService = _ref.read(chatServiceProvider);
        _isCurrentUserClient().then((isClient) {
          if (newMessage.senderType != (isClient ? 'client' : 'artisan')) {
            chatService.markMessagesAsRead(
              conversationId,
              isClient ? 'client' : 'artisan',
            );
          }
        });
      } else {
        if (kDebugMode) {
          print('Message already exists, skipping');
        }
      }
    } else {
      if (kDebugMode) {
        print('State has no value, cannot add new message');
      }
    }
  }

  // Add this method to properly handle updated messages
  void _handleUpdatedMessage(Message updatedMessage) {
    if (state.hasValue) {
      if (kDebugMode) {
        print(
          '[$conversationId] Handling updated message: ${updatedMessage.id}',
        );
      }
      final updatedMessages = [...state.value!];
      final index = updatedMessages.indexWhere(
        (msg) => msg.id == updatedMessage.id,
      );

      if (index != -1) {
        // Replace the existing message with the updated one
        if (kDebugMode) {
          print('[$conversationId] Found message at index $index, updating');
        }
        updatedMessages[index] = updatedMessage;
        state = AsyncValue.data(updatedMessages);
        if (kDebugMode) {
          print('[$conversationId] State updated with modified message');
        }
      } else {
        if (kDebugMode) {
          print('[$conversationId] Message not found in state, cannot update');
        }
      }
    } else {
      if (kDebugMode) {
        print('[$conversationId] State has no value, cannot update message');
      }
    }
  }

  // Handle deleted messages
  void handleDeletedMessage(int deletedMessageId) {
    if (kDebugMode) {
      print('[$conversationId] Handling deleted message: $deletedMessageId');
    }

    if (!state.hasValue) {
      if (kDebugMode) {
        print('[$conversationId] State has no value, cannot delete message');
      }
      return;
    }

    // Create a new list without the deleted message
    final updatedMessages =
        state.value!.where((msg) => msg.id != deletedMessageId).toList();

    if (updatedMessages.length != state.value!.length) {
      // Only update state if a message was actually removed
      if (kDebugMode) {
        print(
          '[$conversationId] Message removed, updating state with ${updatedMessages.length} messages',
        );
      }

      // Force a state update by creating a new AsyncValue
      state = AsyncValue.data(updatedMessages);

      if (kDebugMode) {
        print('[$conversationId] State updated after message deletion');
      }
    } else {
      if (kDebugMode) {
        print(
          '[$conversationId] Message not found in state, nothing to delete. ID: $deletedMessageId',
        );
      }
    }
  }

  Future<void> sendMessage(String content) async {
    try {
      final chatService = _ref.read(chatServiceProvider);

      // Determine if current user is client or artisan
      final isClient = await _isCurrentUserClient();

      // Get the current user ID
      final supabase = Supabase.instance.client;
      final supabaseId = supabase.auth.currentUser?.id;

      if (supabaseId == null) {
        if (kDebugMode) {
          print('Error: User not logged in');
        }
        return;
      }

      // Get the user ID based on type
      final userResponse =
          await supabase
              .from(isClient ? 'clients' : 'artisans')
              .select('id')
              .eq('supabase_id', supabaseId)
              .single();

      final userId = userResponse['id'];

      // Show optimistic UI update with a temporary message
      if (state.hasValue) {
        final tempMessage = Message(
          id: -1, // Use negative ID for temp messages
          conversationId: conversationId,
          senderType: isClient ? 'client' : 'artisan',
          senderId: userId,
          content: content,
          isRead: false,
          createdAt: DateTime.now().toIso8601String(),
        );

        // Add temporary message to the UI
        final updatedMessages = [...state.value!, tempMessage];
        state = AsyncValue.data(updatedMessages);
      }

      // Actually send the message
      await chatService.sendMessage(
        conversationId: conversationId,
        senderType: isClient ? 'client' : 'artisan',
        senderId: userId,
        content: content,
      );

      // The real message will replace the temporary one via the realtime subscription
    } catch (e) {
      // Handle error
      if (kDebugMode) {
        print('Error sending message: $e');
      }
    }
  }

  @override
  void dispose() {
    _typingTimer?.cancel();
    _channel?.unsubscribe();
    _presenceChannel?.unsubscribe();
    super.dispose();
  }

  Future<bool> _isCurrentUserClient() async {
    final supabase = Supabase.instance.client;
    final supabaseId = supabase.auth.currentUser?.id;

    if (supabaseId == null) return false;

    // Check if user exists in clients table
    final clientResponse =
        await supabase
            .from('clients')
            .select('id')
            .eq('supabase_id', supabaseId)
            .maybeSingle();

    // If found in clients table, user is a client
    return clientResponse != null;
  }
}

class ChatScreen extends ConsumerStatefulWidget {
  final int conversationId;
  final String otherPartyName;
  final String? otherPartyAvatar;

  const ChatScreen({
    super.key,
    required this.conversationId,
    required this.otherPartyName,
    this.otherPartyAvatar,
  });

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _shouldScrollToBottom = true;
  bool _isManualScrolling = false;

  @override
  void initState() {
    super.initState();

    // Add scroll listener with improved logic
    _scrollController.addListener(() {
      if (!_scrollController.hasClients || _isManualScrolling) return;

      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      final isAtBottom = maxScroll - currentScroll <= 50;

      if (_shouldScrollToBottom != isAtBottom) {
        setState(() {
          _shouldScrollToBottom = isAtBottom;
        });
      }
    });
    // Set up polling to check for deleted messages
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();

    super.dispose();
  }

  // Improved scroll to bottom function
  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;

    try {
      _isManualScrolling = true;
      final maxScroll = _scrollController.position.maxScrollExtent;
      _scrollController
          .animateTo(
            maxScroll,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          )
          .then((_) {
            _isManualScrolling = false;
          });
    } catch (e) {
      _isManualScrolling = false;
      if (kDebugMode) {
        print('Error scrolling to bottom: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final messagesAsync = ref.watch(
      chatControllerProvider(widget.conversationId),
    );

   // final state = ref.watch(chatViewModelProvider);

    // Add a key based on the messages hash to force rebuild when content changes
    final messagesKey =
        messagesAsync.hasValue
            ? ValueKey(
              messagesAsync.value?.map((m) => '${m.id}:${m.content}').join(','),
            )
            : const ValueKey('loading');

    // Watch for typing indicator from other party
    final otherPartyTyping = ref.watch(
      otherPartyTypingProvider(widget.conversationId),
    );

    // Auto-scroll to bottom when new messages arrive and user was already at bottom
    if (messagesAsync.hasValue &&
        _shouldScrollToBottom &&
        !_isManualScrolling) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: darkBlueColor,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Row(
          children: [
            CircleAvatar(
              backgroundImage:
                  widget.otherPartyAvatar != null
                      ? NetworkImage(widget.otherPartyAvatar!)
                      : const AssetImage('assets/images/profile_pic.png')
                          as ImageProvider,
              radius: 20,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.otherPartyName,
                  style: MyTypography.SemiBold.copyWith(
                    fontSize: 18,
                    color: Colors.white,
                  ),
                ),
                if (otherPartyTyping)
                  Text(
                    'Typing...',
                    style: MyTypography.Regular.copyWith(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.call, color: Colors.white),
            onPressed: () {
              // Handle call action
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () {
              // Handle more options
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: GestureDetector(
                // Cancel auto-scroll when user touches the screen
                onTapDown: (_) => setState(() => _shouldScrollToBottom = false),
                child: messagesAsync.when(
                  data: (messages) {
                    if (messages.isEmpty) {
                      return const Center(
                        child: Text('No messages yet. Start the conversation!'),
                      );
                    }

                    // Ensure messages are sorted with oldest first
                    final sortedMessages = List<Message>.from(messages);
                    sortedMessages.sort(
                      (a, b) => DateTime.parse(
                        a.createdAt,
                      ).compareTo(DateTime.parse(b.createdAt)),
                    );

                    return FutureBuilder<bool>(
                      future:
                          ref
                              .read(
                                chatControllerProvider(
                                  widget.conversationId,
                                ).notifier,
                              )
                              ._isCurrentUserClient(),
                      builder: (context, snapshot) {
                        final isClient =
                            snapshot.data ??
                            true; // Default to client if not loaded yet

                        return ListView.builder(
                          key: messagesKey,
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: sortedMessages.length,
                          itemBuilder: (context, index) {
                            final message = sortedMessages[index];
                            final isFromMe =
                                message.senderType ==
                                (isClient ? 'client' : 'artisan');

                            return _buildMessageBubble(message, isFromMe);
                          },
                        );
                      },
                    );
                  },
                  loading:
                      () => const Center(child: CircularProgressIndicator()),
                  error:
                      (error, stack) =>
                          Center(child: Text('Error loading messages: $error')),
                ),
              ),
            ),
            _buildMessageInput(),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(Message message, bool isFromMe) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isFromMe && widget.otherPartyAvatar != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: CircleAvatar(
                backgroundImage:
                    widget.otherPartyAvatar != null
                        ? NetworkImage(widget.otherPartyAvatar!)
                        : const AssetImage('assets/images/profile_pic.png')
                            as ImageProvider,
                radius: 16,
              ),
            ),

          Flexible(
            child: GestureDetector(
              onLongPress: isFromMe ? () => _showMessageOptions(message) : null,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: isFromMe ? darkBlueColor : Colors.grey[200],
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha((0.05 * 255).round()),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.content,
                      style: TextStyle(
                        color: isFromMe ? Colors.white : Colors.black87,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatMessageTime(message.createdAt),
                      style: TextStyle(
                        color: isFromMe ? Colors.white70 : Colors.black54,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessageOptions(Message message) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final isDeleting = ref.watch(
                    isDeletingMessageProvider(message.id),
                  );

                  return ListTile(
                    leading: const Icon(Icons.delete),
                    title: const Text('Delete Message'),
                    trailing:
                        isDeleting
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : null,
                    enabled: !isDeleting,
                    onTap: () {
                      Navigator.pop(context);
                      _deleteMessage(message.id);
                    },
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Message'),
                onTap: () {
                  Navigator.pop(context);
                  _editMessage(message);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _deleteMessage(int messageId) async {
    try {
      // Set deleting state to true
      ref.read(isDeletingMessageProvider(messageId).notifier).state = true;

      final chatService = ref.read(chatServiceProvider);
      if (kDebugMode) {
        print('Sending delete request for message ID: $messageId');
      }
      await chatService.deleteMessage(messageId);
      if (kDebugMode) {
        print('Message deletion request sent for ID: $messageId');
      }

      // Manually trigger the deletion in the UI
      final controller = ref.read(
        chatControllerProvider(widget.conversationId).notifier,
      );
      controller.handleDeletedMessage(messageId);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Message deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting message: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to delete message: $e')));
      }
    } finally {
      // Set deleting state back to false
      ref.read(isDeletingMessageProvider(messageId).notifier).state = false;
    }
  }

  void _editMessage(Message message) {
    // Use a StatefulBuilder to manage the controller's lifecycle
    String editedContent = message.content;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        // Create a new controller inside the builder
        final TextEditingController controller = TextEditingController(
          text: message.content,
        );

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Edit Message'),
              content: TextField(
                controller: controller,
                decoration: const InputDecoration(
                  hintText: 'Edit your message',
                  border: OutlineInputBorder(),
                ),
                maxLines: null,
                autofocus: true,
                onChanged: (value) {
                  // Update the content as the user types
                  editedContent = value;
                },
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(dialogContext);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(dialogContext, true);
                  },
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // Only proceed if the user pressed Save (result is true)
      if (result == true &&
          editedContent.trim().isNotEmpty &&
          editedContent.trim() != message.content) {
        try {
          final chatService = ref.read(chatServiceProvider);
          chatService.updateMessage(message.id, editedContent.trim());
          if (kDebugMode) {
            print('Message update request sent for ID: ${message.id}');
          }
          // The UI will be updated via the realtime subscription
        } catch (e) {
          if (kDebugMode) {
            print('Error updating message: $e');
          }
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to update message: $e')),
            );
          }
        }
      }
    });
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.05 * 255).round()),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.attach_file),
              color: Colors.grey[600],
              onPressed: () {
                // Handle attachment
              },
            ),
            Expanded(
              child: TextField(
                controller: _messageController,
                decoration: InputDecoration(
                  hintText: 'Type a message...',
                  hintStyle: TextStyle(color: Colors.grey[400]),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                ),
                textCapitalization: TextCapitalization.sentences,
                minLines: 1,
                maxLines: 5,
                onChanged: (text) {
                  // Trigger typing indicator when user types
                  if (text.isNotEmpty) {
                    final controller = ref.read(
                      chatControllerProvider(widget.conversationId).notifier,
                    );
                    controller.startTyping();
                  }
                },
                onSubmitted: (text) {
                  if (text.trim().isNotEmpty) {
                    _sendMessage();
                  }
                },
              ),
            ),
            const SizedBox(width: 8),
            Consumer(
              builder: (context, ref, child) {
                final isLoading = ref.watch(isSendingMessageProvider);
                return CircleAvatar(
                  backgroundColor: darkBlueColor,
                  radius: 24,
                  child:
                      isLoading
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : IconButton(
                            icon: const Icon(Icons.send, color: Colors.white),
                            onPressed: _sendMessage,
                          ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      final controller = ref.read(
        chatControllerProvider(widget.conversationId).notifier,
      );
      ref.read(isSendingMessageProvider.notifier).state = true;

      // Stop typing indicator when sending a message
      controller.stopTyping();

      controller.sendMessage(_messageController.text.trim()).then((_) {
        _messageController.clear();
        ref.read(isSendingMessageProvider.notifier).state = false;

        // Force scroll to bottom after sending
        setState(() {
          _shouldScrollToBottom = true;
        });

        // Scroll to bottom after a short delay to allow the UI to update
        Future.delayed(const Duration(milliseconds: 100), () {
          _scrollToBottom();
        });
      });
    }
  }

  String _formatMessageTime(String timestamp) {
    final messageTime = DateTime.parse(timestamp).toLocal();
    final now = DateTime.now();
    final difference = now.difference(messageTime);

    // If less than 1 minute ago, show "Now"
    if (difference.inMinutes < 1) {
      return 'Now';
    }
    // If less than 1 hour ago, show "X minutes ago"
    else if (difference.inHours < 1) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    }
    // If today, show time only
    else if (difference.inDays < 1) {
      return DateFormat('h:mm a').format(messageTime);
    }
    // If yesterday
    else if (difference.inDays == 1 ||
        (now.day != messageTime.day &&
            now.difference(DateTime(now.year, now.month, now.day)).inDays ==
                1)) {
      return 'Yesterday, ${DateFormat('h:mm a').format(messageTime)}';
    }
    // If within the last 7 days
    else if (difference.inDays < 7) {
      return '${DateFormat('EEEE').format(messageTime)}, ${DateFormat('h:mm a').format(messageTime)}';
    }
    // Otherwise show date and time
    else {
      return DateFormat('MMM d, h:mm a').format(messageTime);
    }
  }
}
