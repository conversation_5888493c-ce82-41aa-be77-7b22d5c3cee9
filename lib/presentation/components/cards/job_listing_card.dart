import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:timeago/timeago.dart' as timeago;

class JobListingCard extends StatelessWidget {
  final String clientName;
  final String clientAvatarUrl;
  final String mainCategory;
  final List<String> tags;
  final DateTime serviceDate;
  final String? description;
  final DateTime postedAt;
  final double? budget;
  final int bidCount;
  final VoidCallback onTap;
  final VoidCallback? onBidTap;
  final VoidCallback? onCancelBidTap;
  final VoidCallback? onCompleteJobTap;
  final bool hasMyBid;
  final bool isSelected;
  final bool isLoading;
  final String status;

  const JobListingCard({
    super.key,
    required this.clientName,
    required this.clientAvatarUrl,
    required this.mainCategory,
    required this.tags,
    required this.serviceDate,
    required this.description,
    required this.postedAt,
    required this.budget,
    required this.bidCount,
    required this.onTap,
    this.onBidTap,
    this.onCancelBidTap,
    this.onCompleteJobTap,
    this.hasMyBid = false,
    this.isSelected = false,
    this.isLoading = false,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Client info row
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.grey[200],
                    backgroundImage:
                        clientAvatarUrl.isEmpty
                            ? null
                            : clientAvatarUrl.startsWith('http')
                            ? NetworkImage(clientAvatarUrl) as ImageProvider
                            : AssetImage(clientAvatarUrl),
                    onBackgroundImageError: (_, __) {
                      if (kDebugMode) {
                        print('Error loading image: $clientAvatarUrl');
                      }
                    },
                    child:
                        clientAvatarUrl.isEmpty ||
                                clientAvatarUrl.contains('error')
                            ? Icon(
                              Icons.person,
                              size: 20,
                              color: Colors.grey[400],
                            )
                            : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          clientName,
                          style: MyTypography.SemiBold.copyWith(fontSize: 16),
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (status == 'open')
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green[100],
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.green[400]!, width: 1),
                            ),
                            child: Text(
                              'Open',
                              style: MyTypography.Medium.copyWith(
                                fontSize: 10,
                                color: Colors.green[700],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (budget != null) ...[
                    Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: orangeColor.withAlpha((0.1 * 255).round()),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '\$${budget?.toStringAsFixed(0)}',
                            style: MyTypography.SemiBold.copyWith(
                              fontSize: 14,
                              color: orangeColor,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            'Est budget',
                            style: MyTypography.Regular.copyWith(
                              color: Colors.grey[600],
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 16),

              // Main category
              Text(
                mainCategory,
                style: MyTypography.SemiBold.copyWith(
                  fontSize: 18,
                  color: darkBlueColor,
                ),
              ),

              const SizedBox(height: 8),

              // Tags
              SizedBox(
                height: 32,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: tags.length > 3 ? 3 : tags.length,
                  itemBuilder: (context, index) {
                    // Create a unique color for each tag based on its index
                    final tagColors = [
                      const Color(0xFFE3F2FD), // Light blue
                      const Color(0xFFE8F5E9), // Light green
                      const Color(0xFFFFF3E0), // Light orange
                    ];
                    final textColors = [
                      const Color(0xFF1565C0), // Dark blue
                      const Color(0xFF2E7D32), // Dark green
                      const Color(0xFFE65100), // Dark orange
                    ];

                    // Use modulo to cycle through colors if there are more than 3 tags
                    final colorIndex = index % tagColors.length;

                    return Padding(
                      padding: EdgeInsets.only(
                        right:
                            index != (tags.length > 3 ? 2 : tags.length - 1)
                                ? 8
                                : 0,
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: tagColors[colorIndex],
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(
                                (0.05 * 255).round(),
                              ),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Text(
                          tags[index],
                          style: MyTypography.Medium.copyWith(
                            fontSize: 12,
                            color: textColors[colorIndex],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Service date
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Needed by: ${_formatDate(serviceDate)}',
                    style: MyTypography.Medium.copyWith(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),

              // Description (if available)
              if (description != null) ...[
                const SizedBox(height: 12),
                Text(
                  description ?? '',
                  style: MyTypography.Regular.copyWith(
                    fontSize: 14,
                    color: Colors.grey[800],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 16),

              // Posted time and bid button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Posted time and bid count
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.access_time,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            timeago.format(postedAt),
                            style: MyTypography.Regular.copyWith(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      status == 'open'
                          ? Row(
                            children: [
                              Icon(
                                Icons.people_outline,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '$bidCount ${bidCount == 1 ? 'bid' : 'bids'} so far',
                                style: MyTypography.Medium.copyWith(
                                  fontSize: 12,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          )
                          : Container(),
                    ],
                  ),

                  // Bid, Cancel Bid, or Complete Job button based on status and selection
                  if (status == 'completed' && hasMyBid && isSelected)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.green[400]!,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 18,
                            color: Colors.green[700],
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Job Completed',
                            style: MyTypography.SemiBold.copyWith(
                              fontSize: 14,
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                    )
                  else if (hasMyBid && isSelected && status == 'in_progress')
                    ElevatedButton.icon(
                      onPressed: onCompleteJobTap,
                      icon: const Icon(Icons.check_circle_outline, size: 18),
                      label: const Text('Close Job'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                      ),
                    )
                      // No button shown when status is open - we're showing status label instead
                  else if (hasMyBid)
                    ElevatedButton.icon(
                      onPressed: onCancelBidTap,
                      icon: const Icon(Icons.cancel_outlined, size: 18),
                      label: const Text('Cancel Bid'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[400],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                      ),
                    )
                  else if (status != 'open' && status != 'new')
                    ElevatedButton(
                      onPressed: isLoading ? null : onBidTap,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: orangeColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                      ),
                      child:
                          isLoading
                              ? SizedBox(
                                width: 80,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Bidding...',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                  ],
                                ),
                              )
                              : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.handshake_outlined,
                                    size: 18,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('Bid Now'),
                                ],
                              ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
