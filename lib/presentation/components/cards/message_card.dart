import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MessageCard extends ConsumerWidget {
  final String senderName;
  final String message;
  final String time;
  final String avatarUrl;
  final bool isUnread;
  final VoidCallback onTap;

  const MessageCard({
    super.key,
    required this.senderName,
    required this.message,
    required this.time,
    required this.avatarUrl,
    this.isUnread = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    if (kDebugMode) {
      print('Building MessageCard for $senderName');
      print('Avatar URL: $avatarUrl');
      print('Is network image: ${avatarUrl.startsWith('http')}');
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow:
              themeMode == ThemeMode.light
                  ? [
                    BoxShadow(
                      color: Colors.black.withAlpha((0.05 * 255).round()),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
          border:
              themeMode == ThemeMode.dark
                  ? Border.all(
                    color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                    width: 1,
                  )
                  : null,
        ),
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 24,
              backgroundImage:
                  avatarUrl.startsWith('http')
                      ? NetworkImage(avatarUrl) as ImageProvider
                      : AssetImage(avatarUrl),
              backgroundColor:
                  themeMode == ThemeMode.light
                      ? Colors.grey[200]
                      : customColors.surfaceVariant,
            ),
            const SizedBox(width: 12),
            // Message Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    senderName,
                    style: MyTypography.SemiBold.copyWith(
                      fontSize: 16,
                      color: customColors.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message,
                    style: MyTypography.Regular.copyWith(
                      fontSize: 14,
                      color: customColors.textPrimaryColor.withValues(
                        alpha: 0.7,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            // Time
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  time,
                  style: MyTypography.Regular.copyWith(
                    fontSize: 12,
                    color: customColors.textPrimaryColor.withValues(alpha: 0.6),
                  ),
                ),
                if (isUnread)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color:
                          themeMode == ThemeMode.light
                              ? orangeColor
                              : orangeColor.withValues(alpha: 0.9),
                      shape: BoxShape.circle,
                      boxShadow:
                          themeMode == ThemeMode.light
                              ? [
                                BoxShadow(
                                  color: orangeColor.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                ),
                              ]
                              : null,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
